<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Milestone Tree View -->
    <record id="view_milestone_tree" model="ir.ui.view">
        <field name="name">project.milestone.tree</field>
        <field name="model">project.milestone</field>
        <field name="arch" type="xml">
            <tree string="Milestones" decoration-danger="blocked_by_dependencies" decoration-success="status == 'completed'">
                <field name="name"/>
                <field name="project_id"/>
                <field name="assigned_to"/>
                <field name="status"/>
                <field name="priority" widget="priority"/>
                <field name="date_due"/>
                <field name="dependency_status"/>
                <field name="can_start" widget="boolean_toggle"/>
                <field name="blocked_by_dependencies" invisible="1"/>
                <field name="time_taken"/>
            </tree>
        </field>
    </record>

    <!-- Milestone Form View -->
    <record id="view_milestone_form" model="ir.ui.view">
        <field name="name">project.milestone.form</field>
        <field name="model">project.milestone</field>
        <field name="arch" type="xml">
            <form string="Milestone">
                <header>
                    <button name="action_start_milestone" string="Start Milestone" type="object"
                            class="btn-primary" invisible="status != 'pending' or blocked_by_dependencies"/>
                    <button name="action_mark_complete" string="Mark Complete" type="object"
                            class="btn-success" invisible="status != 'in_progress' or blocked_by_dependencies"/>
                    <button name="action_view_dependency_graph" string="View Dependency Tree" type="object"
                            class="btn-secondary" invisible="not dependency_ids"/>
                    <field name="status" widget="statusbar" statusbar_visible="pending,in_progress,completed"/>
                </header>
                <sheet>
                    <div class="alert alert-warning" role="alert" invisible="not blocked_by_dependencies">
                        <strong>⚠️ Blocked by Dependencies:</strong> This milestone cannot be started until all dependencies are completed.
                        <br/>
                        <field name="dependency_status" readonly="1" nolabel="1"/>
                    </div>
                    <div class="alert alert-success" role="alert" invisible="not can_start or blocked_by_dependencies">
                        <strong>✅ Ready to Start:</strong> All dependencies are completed. This milestone can be started.
                    </div>
                    
                    <group>
                        <group>
                            <field name="name" string="Milestone Name"/>
                            <field name="project_id" string="Project"/>
                            <field name="assigned_to" string="Assigned To"/>
                            <field name="priority" widget="priority"/>
                            <field name="date_due" string="Due Date"/>
                        </group>
                        <group>
                            <field name="time_taken" string="Time Taken" readonly="1"/>
                            <field name="date_in_progress" string="Started On" readonly="1"/>
                            <field name="date_completed" string="Completed On" readonly="1"/>
                            <field name="can_start" string="Can Start" readonly="1"/>
                            <field name="blocked_by_dependencies" invisible="1"/>
                            <field name="mark_as_complete" widget="boolean_toggle" string="Mark as Completed"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Dependencies" name="dependencies">
                            <group>
                                <group string="Prerequisites">
                                    <field name="dependency_ids" widget="many2many" nolabel="1"
                                           domain="[('project_id', '=', project_id), ('id', '!=', id)]"
                                           help="Milestones that must be completed before this one can be started">
                                        <tree>
                                            <field name="name"/>
                                            <field name="status"/>
                                            <field name="date_due"/>
                                            <field name="assigned_to"/>
                                        </tree>
                                    </field>
                                </group>
                                <group string="Dependent Milestones">
                                    <field name="dependent_milestone_ids" widget="many2many" nolabel="1" readonly="1"
                                           help="Milestones that depend on this one being completed">
                                        <tree>
                                            <field name="name"/>
                                            <field name="status"/>
                                            <field name="date_due"/>
                                            <field name="assigned_to"/>
                                        </tree>
                                    </field>
                                </group>
                            </group>
                        </page>
                        
                        <page string="Job Cards" name="job_cards">
                            <field name="job_card_ids">
                                <tree editable="bottom">
                                    <field name="card_name"/>
                                    <field name="state"/>
                                    <field name="user_id"/>
                                    <field name="deadline"/>
                                    <field name="start_date"/>
                                    <field name="end_date"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="Comments" name="comments">
                            <field name="comments" placeholder="Add any comments or notes about this milestone..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Milestone Kanban View -->
    <record id="view_milestone_kanban" model="ir.ui.view">
        <field name="name">project.milestone.kanban</field>
        <field name="model">project.milestone</field>
        <field name="arch" type="xml">
            <kanban default_group_by="status" class="o_kanban_small_column">
                <field name="name"/>
                <field name="project_id"/>
                <field name="assigned_to"/>
                <field name="status"/>
                <field name="priority"/>
                <field name="date_due"/>
                <field name="can_start"/>
                <field name="blocked_by_dependencies"/>
                <field name="dependency_status"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click #{record.blocked_by_dependencies.raw_value ? 'oe_kanban_color_2' : ''}">
                            <div class="o_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                    <div class="o_kanban_record_top_right">
                                        <field name="priority" widget="priority"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>Project: <field name="project_id"/></div>
                                    <div t-if="record.assigned_to.raw_value">Assigned: <field name="assigned_to"/></div>
                                    <div t-if="record.date_due.raw_value">Due: <field name="date_due"/></div>
                                    <div t-if="record.dependency_status.raw_value">
                                        <span t-if="record.blocked_by_dependencies.raw_value" class="text-danger">🚫</span>
                                        <span t-if="record.can_start.raw_value and !record.blocked_by_dependencies.raw_value" class="text-success">✅</span>
                                        <field name="dependency_status"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Milestone Search View -->
    <record id="view_milestone_search" model="ir.ui.view">
        <field name="name">project.milestone.search</field>
        <field name="model">project.milestone</field>
        <field name="arch" type="xml">
            <search string="Milestones">
                <field name="name"/>
                <field name="project_id"/>
                <field name="assigned_to"/>
                <separator/>
                <filter string="Can Start" name="can_start" domain="[('can_start', '=', True)]"/>
                <filter string="Blocked" name="blocked" domain="[('blocked_by_dependencies', '=', True)]"/>
                <filter string="Pending" name="pending" domain="[('status', '=', 'pending')]"/>
                <filter string="In Progress" name="in_progress" domain="[('status', '=', 'in_progress')]"/>
                <filter string="Completed" name="completed" domain="[('status', '=', 'completed')]"/>
                <separator/>
                <filter string="High Priority" name="high_priority" domain="[('priority', 'in', ['2', '3'])]"/>
                <filter string="Overdue" name="overdue" domain="[('date_due', '&lt;', context_today().strftime('%Y-%m-%d')), ('status', '!=', 'completed')]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Project" name="group_project" context="{'group_by': 'project_id'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Assigned To" name="group_assigned" context="{'group_by': 'assigned_to'}"/>
                    <filter string="Priority" name="group_priority" context="{'group_by': 'priority'}"/>
                    <filter string="Due Date" name="group_due_date" context="{'group_by': 'date_due'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Milestone Action -->
    <record id="action_milestone" model="ir.actions.act_window">
        <field name="name">Milestones</field>
        <field name="res_model">project.milestone</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="view_milestone_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No milestones found. Let's create one!
            </p>
            <p>
                Milestones help you track important project deliverables and manage dependencies between tasks.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_milestone" name="Milestones" parent="project.menu_main_pm" action="action_milestone" sequence="3"/>

</odoo>
