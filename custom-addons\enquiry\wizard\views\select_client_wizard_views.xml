<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Select Client Wizard Form View -->
    <record id="view_select_client_wizard_form" model="ir.ui.view">
        <field name="name">enquiry.select.client.wizard.form</field>
        <field name="model">enquiry.select.client.wizard</field>
        <field name="arch" type="xml">
            <form string="Select Existing Client">
                <p class="text-muted">
                    Select an existing client to populate the enquiry with their information.
                </p>
                <group>
                    <field name="client_id" options="{'no_create': True}" domain="[]"/>
                </group>
                <footer>
                    <button name="action_apply_client" string="Apply" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action to open the wizard -->
    <record id="action_select_client_wizard" model="ir.actions.act_window">
        <field name="name">Select Existing Client</field>
        <field name="res_model">enquiry.select.client.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" ref="model_enquiry_enquiry"/>
    </record>
</odoo>