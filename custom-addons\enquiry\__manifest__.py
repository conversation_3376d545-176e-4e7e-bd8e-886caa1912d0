{
    'name': 'Enquiry',
    'version': '1.0',
    'author': 'T-Works',
    'category': 'Sales/CRM',
    'summary': 'Custom Enquiry Module',
    'description': """
        This module adds a custom enquiry solution:
            - Two-stage form process for clients
            - NDA Handling
            - Stage-based workflow
            - Detailed tracking of requirements and quotes
            - Custom cost item management
            - Pro Forma Invoice generation
            - API integration with external systems
            - Client selection from CRM module
            - Payment integration with Razorpay
    """,
    'readme': 'README.md',
    'depends': ['base', 'mail', 'crm', 'hr', 'crm_extension'],
    'data': [
       # Security
        'security/enquiry_security.xml',
        'security/enquiry_security_groups.xml',
        'security/invoice_securtiy_rules.xml',
        'security/enquiry_accounts_security_groups.xml',
        'security/ir.model.access.csv',
 
        # Data
        'data/enquiry_sequence.xml',
        'data/default_stages.xml',
        'data/fabrication_capability_data.xml',
        'data/proforma_invoice_sequence.xml',
        'data/invoice_sequence.xml',
        'data/init_config_params.xml',
 
        # Reports
        'reports/proforma_invoice_report.xml',
        'reports/invoice_report.xml',
 
        # Views
        'views/res_config_settings_views.xml',
        'views/enquiry_stages_views.xml',
        'views/menu_views.xml',
        'views/enquiry_views.xml',
        'views/custom_line_items_views.xml',
        'views/registration_status_views.xml',
        'views/enquiry_api_views.xml',
        'views/enquiry_api_views_fix.xml',
        'views/fabrication_capability_views.xml',
        'views/pre_sales_approval_view.xml',
        'views/fix_cost_estimates_view.xml',
        'views/proforma_invoice_views.xml',
        'views/proforma_invoice_access_view.xml',
        'views/invoice_views.xml',
        'views/enquiry_invoice_button_view.xml',
        'views/update_enquiry_view.xml',
        'views/templates/enquiry_templates.xml',
        'views/enquiry_form_view_inherit.xml',
        'views/payment_views.xml',
        'views/enquiry_payment_views_enhance.xml',
        'views/close_button_enhancements.xml',
        'views/payment_wizards_views.xml',
        'views/enquiry_view_razorpay.xml',
        'views/copy_to_clipboard_view.xml',
        'views/fix_active_tab.xml',
        'views/pre_sales_view_fix.xml',
 
        # Wizards
        'wizard/views/close_wizard_views.xml',
        'wizard/views/cost_estimate_wizard_views.xml',
        'wizard/views/select_client_wizard_views.xml',
        'wizard/views/edit_client_wizard_views.xml',
        'wizard/views/enquiry_invoice_update_wizard_view.xml',
        'wizard/views/invoice_lines_wizard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'enquiry/static/src/js/clipboard_action.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
}
