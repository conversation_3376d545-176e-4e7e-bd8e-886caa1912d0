<odoo>
  <!-- Kanban View for Custom Projects -->
  <record id="view_project_kanban_custom" model="ir.ui.view">
    <field name="name">project.project.kanban.custom</field>
    <field name="model">project.project</field>
    <field name="arch" type="xml">
      <kanban class="o_kanban_small_column" create="1" edit="1" quick_create="0" group_delete="1" group_edit="1" default_group_by="stage_id" records_draggable="0">
        <field name="stage_id"/>
        <field name="name"/>
        <field name="current_stage_days"/>
        <field name="project_custom_id"/>
        <field name="project_poc"/>
        <field name="budget"/>
        <field name="date_start"/>
        <field name="date"/>
        <field name="budget_remaining"/>
        <templates>
          <t t-name="kanban-box">
            <div class="oe_kanban_card oe_kanban_global_click" style="padding: 0; border-radius: 8px; overflow: hidden;">
              <div class="bg-light d-flex justify-content-between align-items-center p-2" style="border-bottom: 1px solid #e0e0e0;">
                <strong class="o_kanban_record_title"><field name="name"/>
                </strong>
                <span t-if="record.current_stage_days"
                    class="badge text-bg-info"
                    style="font-weight:bold; font-size:0.95em; margin-left:8px;">
                  <t t-esc="record.current_stage_days.raw_value"/> days
                </span>
                </div>
                <div>
                  <t t-if="record.stage_id and record.stage_id.data and record.stage_id.data.state">
                    <t t-if="record.stage_id.data.state.raw_value == 'active'">
                      <span class="badge text-bg-success" style="font-weight:bold; font-size:1.1em;">Active</span>
                    </t>
                    <t t-else="">
                      <t t-if="record.stage_id.data.state.raw_value == 'completed'">
                        <span class="badge text-bg-primary" style="font-weight:bold; font-size:1.1em;">Completed</span>
                      </t>
                      <t t-else="">
                        <span class="badge text-bg-danger" style="font-weight:bold; font-size:1.1em;">Closed</span>
                      </t>
                    </t>
                  </t>
                </div>
              <div class="p-3">
                <div class="mb-2"><strong>POC:</strong> <field name="project_poc"/></div>
                <div class="mb-2"><strong>Budget:</strong> <field name="budget"/></div>
                <div class="mb-2"><strong>Start Date:</strong> <field name="date_start"/></div>
                <div class="mb-2"><strong>Due Date:</strong> <field name="date"/></div>
                
                <div class="mb-2"><strong>Budget Remaining:</strong>
                <div class="mb-2"><strong>Pending Requests: </strong><field name="pending_request_count"/></div>
                <span t-if="record.budget_remaining"
                    class="badge text-bg-warning"
                    style="font-weight:bold; font-size:0.95em; margin-left:8px;">
                  <t t-esc="record.budget_remaining.raw_value"/>
                </span></div>
              
              </div>
              <div class="d-flex justify-content-between align-items-center p-2 bg-light" style="border-top: 1px solid #e0e0e0;">
                                <div>
                                    <field name="project_poc" widget="many2one_avatar"/>
                                </div>
                            </div>
            </div>
          </t>
        </templates>
      </kanban>
    </field>
  </record>

<!-- Action for Custom Projects -->
  <record id="action_project_project_custom" model="ir.actions.act_window">
    <field name="name">Projects</field>
    <field name="res_model">project.project</field>
    <field name="view_mode">kanban,form,tree</field>
    <field name="view_id" ref="view_project_kanban_custom"/>
    <field name="context">{'form_view_ref': 'project_extension.open_create_project_custom'}</field>
    <field name="views" eval="[(ref('view_project_kanban_custom'), 'kanban'), (ref('project_extension.open_create_project_custom'), 'form'), (False, 'tree')]"/>
    <field name="help" type="html">
      <p class="o_view_nocontent_smiling_face">
        No projects found. Let's create one!
      </p>
      <p>
        Create projects to organize your tasks. Define a different workflow for each project.
      </p>
    </field>
  </record>

  <!-- Menu Items for Custom Projects -->
  <menuitem id="menu_project_custom_root" name="Custom Projects" sequence="1"/>
  <menuitem id="menu_project_custom_projects" name="Milestones" action="action_project_project_custom" sequence="2" parent="menu_project_custom_root"/>
</odoo>