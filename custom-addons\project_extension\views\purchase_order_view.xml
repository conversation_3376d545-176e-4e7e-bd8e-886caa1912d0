<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Purchase Order Form View Extension -->
    <record id="purchase_order_form_job_card_extension" model="ir.ui.view">
        <field name="name">purchase.order.form.job.card.extension</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            
            <!-- Add Job Card and Project buttons to button box -->
            <xpath expr="//div[@name='button_box']" position="inside">
                <button class="oe_stat_button" name="action_view_job_card" type="object" 
                        icon="fa-tasks" invisible="not job_card_id">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Job Card</span>
                    </div>
                </button>
                <button class="oe_stat_button" name="action_view_project" type="object" 
                        icon="fa-folder-open" invisible="not project_id">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Project</span>
                    </div>
                </button>
                <button class="oe_stat_button" name="action_view_milestone" type="object" 
                        icon="fa-flag-checkered" invisible="not milestone_id">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Milestone</span>
                    </div>
                </button>
            </xpath>

            <!-- Add Job Card and Project Information Tab -->
            <xpath expr="//notebook" position="inside">
                <page string="Job Card &amp; Project Info" name="job_card_project_info">
                    <group>
                        <group string="Job Card Information">
                            <field name="job_card_id" options="{'no_create': True}"/>
                            <field name="job_card_state" readonly="1"/>
                            <field name="job_card_deadline" readonly="1"/>
                            <field name="job_card_start_date" readonly="1"/>
                            <field name="job_card_end_date" readonly="1"/>
                            <field name="job_card_assigned_user" readonly="1"/>
                            <field name="job_card_customer" readonly="1"/>
                        </group>
                        <group string="Project Information">
                            <field name="project_id" options="{'no_create': True}"/>
                            <field name="milestone_id" options="{'no_create': True}"/>
                            <field name="workshop_team_id" options="{'no_create': True}"/>
                            <field name="project_stage" readonly="1"/>
                            <field name="project_poc" readonly="1"/>
                            <field name="project_budget" readonly="1"/>
                            <field name="project_budget_remaining" readonly="1"/>
                        </group>
                    </group>
                    <group string="Budget Analysis" invisible="not project_id">
                        <group>
                            <field name="budget_impact_percentage" readonly="1" widget="percentage"/>
                            <field name="is_over_budget" readonly="1"/>
                        </group>
                    </group>
                    <group string="Job Card Description" invisible="not job_card_description">
                        <field name="job_card_description" readonly="1" nolabel="1"/>
                    </group>
                </page>
            </xpath>

            <!-- Add warning for over budget orders -->
            <xpath expr="//form/sheet" position="before">
                <div class="alert alert-warning" role="alert" invisible="not is_over_budget">
                    <strong>Budget Warning:</strong> This purchase order exceeds the remaining project budget.
                    <br/>
                    Order Amount: <field name="amount_total" readonly="1" nolabel="1"/>
                    <br/>
                    Remaining Budget: <field name="project_budget_remaining" readonly="1" nolabel="1"/>
                </div>
            </xpath>

        </field>
    </record>

    <!-- Purchase Order Tree View Extension -->
    <record id="purchase_order_tree_job_card_extension" model="ir.ui.view">
        <field name="name">purchase.order.tree.job.card.extension</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="project_id" optional="show"/>
                <field name="job_card_id" optional="hide"/>
                <field name="milestone_id" optional="hide"/>
                <field name="job_card_state" optional="hide"/>
                <field name="is_over_budget" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- Purchase Order Line Extensions - Temporarily commented out to avoid dependency issues -->
    <!--
    <record id="purchase_order_line_form_job_card_extension" model="ir.ui.view">
        <field name="name">purchase.order.line.form.job.card.extension</field>
        <field name="model">purchase.order.line</field>
        <field name="inherit_id" ref="purchase.purchase_order_line_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='main_group']" position="after">
                <group string="Job Card Instruction" name="job_card_instruction_group">
                    <field name="job_card_instruction_id" options="{'no_create': True}"/>
                    <field name="instruction_capability" readonly="1"/>
                    <field name="instruction_machine" readonly="1"/>
                    <field name="instruction_material" readonly="1"/>
                    <field name="instruction_state" readonly="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="purchase_order_line_tree_job_card_extension" model="ir.ui.view">
        <field name="name">purchase.order.line.tree.job.card.extension</field>
        <field name="model">purchase.order.line</field>
        <field name="inherit_id" ref="purchase.purchase_order_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="job_card_instruction_id" optional="hide"/>
                <field name="instruction_capability" optional="hide"/>
                <field name="instruction_state" optional="hide"/>
            </xpath>
        </field>
    </record>
    -->

    <!-- Search View Extension for Purchase Orders -->
    <record id="purchase_order_search_job_card_extension" model="ir.ui.view">
        <field name="name">purchase.order.search.job.card.extension</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.view_purchase_order_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <field name="job_card_id"/>
                <field name="project_id"/>
                <field name="milestone_id"/>
                <field name="workshop_team_id"/>
                <field name="job_card_assigned_user"/>
                
                <separator/>
                <filter string="Has Job Card" name="has_job_card" domain="[('job_card_id', '!=', False)]"/>
                <filter string="Has Project" name="has_project" domain="[('project_id', '!=', False)]"/>
                <filter string="Over Budget" name="over_budget" domain="[('is_over_budget', '=', True)]"/>
                
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Job Card" name="group_by_job_card" context="{'group_by': 'job_card_id'}"/>
                    <filter string="Project" name="group_by_project" context="{'group_by': 'project_id'}"/>
                    <filter string="Milestone" name="group_by_milestone" context="{'group_by': 'milestone_id'}"/>
                    <filter string="Workshop Team" name="group_by_team" context="{'group_by': 'workshop_team_id'}"/>
                    <filter string="Job Card Status" name="group_by_job_card_state" context="{'group_by': 'job_card_state'}"/>
                </group>
            </xpath>
        </field>
    </record>

</odoo>
