from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    # Job Card related fields
    job_card_id = fields.Many2one('job.card', string="Job Card", tracking=True,
                                  help="Job card associated with this purchase order")
    milestone_id = fields.Many2one('project.milestone', string="Milestone", tracking=True,
                                   help="Project milestone associated with this purchase order")
    workshop_team_id = fields.Many2one('workshop.team', string="Workshop Team", tracking=True,
                                       help="Workshop team responsible for this order")
    job_card_state = fields.Selection(related='job_card_id.state', string="Job Card Status", 
                                      readonly=True, store=True,
                                      help="Current status of the associated job card")
    job_card_deadline = fields.Date(related='job_card_id.deadline', string="Job Card Deadline",
                                    readonly=True, store=True,
                                    help="Deadline from the associated job card")
    job_card_start_date = fields.Date(related='job_card_id.start_date', string="Job Card Start Date",
                                      readonly=True, store=True,
                                      help="Start date from the associated job card")
    job_card_end_date = fields.Date(related='job_card_id.end_date', string="Job Card End Date",
                                    readonly=True, store=True,
                                    help="End date from the associated job card")
    job_card_assigned_user = fields.Many2one(related='job_card_id.user_id', string="Job Card Assigned To",
                                             readonly=True, store=True,
                                             help="User assigned to the job card")
    job_card_customer = fields.Many2one(related='job_card_id.partner_id', string="Job Card Customer",
                                        readonly=True, store=True,
                                        help="Customer from the associated job card")
    job_card_description = fields.Text(related='job_card_id.description', string="Job Card Description",
                                       readonly=True,
                                       help="Description from the associated job card")
    
    # Project related fields (enhanced from existing project_id if available)
    project_id = fields.Many2one('project.project', string="Project", tracking=True,
                                 help="Project associated with this purchase order")
    project_stage = fields.Many2one(related='project_id.stage_id', string="Project Stage",
                                    readonly=True, store=True,
                                    help="Current stage of the associated project")
    project_poc = fields.Many2one(related='project_id.project_poc', string="Project POC",
                                  readonly=True, store=True,
                                  help="Project Point of Contact")
    project_budget = fields.Monetary(related='project_id.budget', string="Project Budget",
                                     readonly=True, store=True, currency_field='currency_id',
                                     help="Total budget of the associated project")
    project_budget_remaining = fields.Monetary(related='project_id.budget_remaining', 
                                               string="Project Budget Remaining",
                                               readonly=True, store=True, currency_field='currency_id',
                                               help="Remaining budget of the associated project")
    
    # Computed fields
    is_over_budget = fields.Boolean(string="Over Budget", compute='_compute_budget_status', store=True,
                                    help="Indicates if this order would exceed project budget")
    budget_impact_percentage = fields.Float(string="Budget Impact %", compute='_compute_budget_status', 
                                            store=True,
                                            help="Percentage of project budget this order represents")
    
    @api.depends('amount_total', 'project_budget_remaining', 'project_budget')
    def _compute_budget_status(self):
        for order in self:
            if order.project_budget and order.project_budget > 0:
                order.budget_impact_percentage = (order.amount_total / order.project_budget) * 100
                order.is_over_budget = order.amount_total > (order.project_budget_remaining or 0)
            else:
                order.budget_impact_percentage = 0.0
                order.is_over_budget = False

    @api.onchange('job_card_id')
    def _onchange_job_card_id(self):
        """Auto-populate related fields when job card is selected"""
        if self.job_card_id:
            # Set project from job card
            if self.job_card_id.project_id:
                self.project_id = self.job_card_id.project_id
            
            # Set milestone from job card
            if self.job_card_id.milestone_id:
                self.milestone_id = self.job_card_id.milestone_id
            
            # Set workshop team from job card
            if self.job_card_id.team_id:
                self.workshop_team_id = self.job_card_id.team_id
            
            # Set partner from job card customer if not already set
            if self.job_card_id.partner_id and not self.partner_id:
                self.partner_id = self.job_card_id.partner_id

    @api.onchange('project_id')
    def _onchange_project_id(self):
        """Filter job cards and milestones based on selected project"""
        if self.project_id:
            return {
                'domain': {
                    'job_card_id': [('project_id', '=', self.project_id.id)],
                    'milestone_id': [('project_id', '=', self.project_id.id)]
                }
            }
        else:
            return {
                'domain': {
                    'job_card_id': [],
                    'milestone_id': []
                }
            }

    def action_view_job_card(self):
        """Action to view the associated job card"""
        self.ensure_one()
        if not self.job_card_id:
            raise UserError(_("No job card is associated with this purchase order."))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Job Card'),
            'res_model': 'job.card',
            'res_id': self.job_card_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_project(self):
        """Action to view the associated project"""
        self.ensure_one()
        if not self.project_id:
            raise UserError(_("No project is associated with this purchase order."))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Project'),
            'res_model': 'project.project',
            'res_id': self.project_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_milestone(self):
        """Action to view the associated milestone"""
        self.ensure_one()
        if not self.milestone_id:
            raise UserError(_("No milestone is associated with this purchase order."))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Milestone'),
            'res_model': 'project.milestone',
            'res_id': self.milestone_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    @api.constrains('amount_total', 'project_budget_remaining')
    def _check_budget_constraint(self):
        """Optional constraint to prevent orders that exceed budget"""
        # This constraint is commented out by default - uncomment if you want to enforce budget limits
        # for order in self:
        #     if order.project_id and order.project_budget_remaining and order.amount_total > order.project_budget_remaining:
        #         raise UserError(_("This purchase order amount (%.2f) exceeds the remaining project budget (%.2f).") % 
        #                        (order.amount_total, order.project_budget_remaining))
        pass

    def write(self, vals):
        """Override write to log budget warnings"""
        result = super().write(vals)
        
        # Log budget warnings
        for order in self:
            if order.is_over_budget and order.project_id:
                _logger.warning(
                    f"Purchase Order {order.name} (Amount: {order.amount_total}) "
                    f"exceeds remaining budget for Project {order.project_id.name} "
                    f"(Remaining: {order.project_budget_remaining})"
                )
        
        return result


# Temporarily commented out to avoid dependency issues
# Will be re-enabled once the main purchase order extension is working
#
# class PurchaseOrderLine(models.Model):
#     _inherit = 'purchase.order.line'
#
#     # Job Card Instruction related fields
#     job_card_instruction_id = fields.Many2one('job.card.instruction', string="Job Card Instruction",
#                                               help="Specific job card instruction this line relates to")
#     instruction_capability = fields.Selection(related='job_card_instruction_id.capability',
#                                               string="Required Capability", readonly=True,
#                                               help="Capability required for this instruction")
#     instruction_machine = fields.Many2one(related='job_card_instruction_id.machine_name',
#                                           string="Required Machine", readonly=True,
#                                           help="Machine required for this instruction")
#     instruction_material = fields.Many2one(related='job_card_instruction_id.material_list',
#                                            string="Instruction Material", readonly=True,
#                                            help="Material specified in the job card instruction")
#     instruction_state = fields.Selection(related='job_card_instruction_id.state',
#                                          string="Instruction Status", readonly=True,
#                                          help="Current status of the job card instruction")
#
#     @api.onchange('job_card_instruction_id')
#     def _onchange_job_card_instruction_id(self):
#         """Auto-populate product from instruction material"""
#         if self.job_card_instruction_id and self.job_card_instruction_id.material_list:
#             # Try to find a product.product that matches the material template
#             product = self.env['product.product'].search([
#                 ('product_tmpl_id', '=', self.job_card_instruction_id.material_list.id)
#             ], limit=1)
#             if product:
#                 self.product_id = product
