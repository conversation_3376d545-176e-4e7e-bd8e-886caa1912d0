from odoo import api, fields, models

class SelectClientWizard(models.TransientModel):
    _name = 'enquiry.select.client.wizard'
    _description = 'Select Existing Client'

    # Apply the filter in the field's domain method instead of in the view
    client_id = fields.Many2one(
        'crm.client',
        string='Select Client',
        required=True
    )
    
    def _get_client_domain(self):
        """Get domain for client_id field, safely checking if fields exist"""
        try:
            # Check if completed_registration field exists on crm.client model
            if hasattr(self.env['crm.client'], '_fields') and 'completed_registration' in self.env['crm.client']._fields:
                return [('completed_registration', '=', True)]
        except Exception:
            # If there's any error accessing the model or fields, return empty domain
            pass
        return []

    # Override this method to check if the domain field exists
    # If not, return empty domain to avoid errors during module installation
    @api.model
    def _search_domain(self):
        return self._get_client_domain()
    
    @api.model
    def default_get(self, fields):
        res = super(SelectClientWizard, self).default_get(fields)
        return res

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        """Override to dynamically set domain for client_id field"""
        result = super().fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)

        # Try to apply domain if the field exists
        try:
            domain = self._get_client_domain()
            if domain and 'fields' in result and 'client_id' in result['fields']:
                result['fields']['client_id']['domain'] = domain
        except Exception:
            # If there's any error, just continue without the domain
            pass

        return result
    
    def action_apply_client(self):
        """Apply the selected client data to the enquiry"""
        self.ensure_one()
        active_id = self.env.context.get('active_id')
        
        if not active_id:
            return {'type': 'ir.actions.act_window_close'}
            
        # Get the currently active enquiry
        enquiry = self.env['enquiry.enquiry'].browse(active_id)
        client = self.client_id
        
        # Only update if client and enquiry exist
        if client and enquiry:
            # Prepare the values to update
            vals = {
                'customer_name': client.name,
                'email': client.email,
                'phone': client.phone,
                'company_name': client.company_name,
                'nda_required': client.nda_required,
                'bd_poc_id': client.bd_poc and client.bd_poc.id or False,
            }
            
            # Update billing address if available
            if client.billing_address:
                # Try to parse the billing address
                address_lines = client.billing_address.split('\n')
                if len(address_lines) >= 1:
                    vals['billing_street'] = address_lines[0]
                    
                    # Try to parse city and zip from second line if available
                    if len(address_lines) >= 2 and ',' in address_lines[1]:
                        city_zip = address_lines[1].split(',')
                        vals['billing_city'] = city_zip[0].strip()
                        if len(city_zip) > 1:
                            vals['billing_zip'] = city_zip[1].strip()
            
            # Update shipping address if available
            if client.shipping_address:
                # Mark shipping as not same as billing since we're explicitly setting it
                vals['shipping_same_as_billing'] = False
                
                # Try to parse the shipping address
                address_lines = client.shipping_address.split('\n')
                if len(address_lines) >= 1:
                    vals['shipping_street'] = address_lines[0]
                    
                    # Try to parse city and zip from second line if available
                    if len(address_lines) >= 2 and ',' in address_lines[1]:
                        city_zip = address_lines[1].split(',')
                        vals['shipping_city'] = city_zip[0].strip()
                        if len(city_zip) > 1:
                            vals['shipping_zip'] = city_zip[1].strip()
            
            # Update the enquiry with client information
            enquiry.write(vals)
            
            # Check if registration should be marked as completed
            if vals.get('customer_name') and vals.get('email') and vals.get('phone') and vals.get('company_name'):
                if vals.get('billing_street') and vals.get('billing_city') and vals.get('billing_zip'):
                    # If shipping address is needed
                    if vals.get('shipping_same_as_billing') or (vals.get('shipping_street') and vals.get('shipping_city') and vals.get('shipping_zip')):
                        enquiry.write({'completed_registration': True})
        
        return {'type': 'ir.actions.act_window_close'}